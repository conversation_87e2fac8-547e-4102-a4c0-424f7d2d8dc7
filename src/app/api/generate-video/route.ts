import { NextRequest, NextResponse } from 'next/server';
import {
  createRunwayMLService,
  prepareImageForRunway,
  createImageToVideoRequest,
  type GenerationResponse
} from '@/lib/runwayml';

interface VideoGenerationRequest {
  imageUrl: string;
  prompt: string;
  duration?: 5 | 10;
  ratio?: '1280:720' | '720:1280' | '1104:832' | '832:1104' | '960:960' | '1584:672' | '1280:768' | '768:1280';
  seed?: number;
}

// Generate video using RunwayML API
async function generateVideoWithRunwayML(
  imageUrl: string,
  prompt: string,
  options: Partial<VideoGenerationRequest> = {}
): Promise<GenerationResponse> {
  try {
    // Create RunwayML service instance
    const runwayService = createRunwayMLService();

    // Prepare image URL for RunwayML
    const preparedImageUrl = await prepareImageForRunway(imageUrl);

    // Create the request
    const request = createImageToVideoRequest(preparedImageUrl, prompt, {
      duration: options.duration || 5,
      ratio: options.ratio || '1280:720',
      seed: options.seed,
    });

    // Generate video and wait for completion
    const result = await runwayService.generateVideoAndWait(request);

    return result;

  } catch (error) {
    console.error('RunwayML generation error:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: VideoGenerationRequest = await request.json();
    const { imageUrl, prompt, duration, ratio, seed } = body;

    // Validate input
    if (!imageUrl || !prompt) {
      return NextResponse.json(
        { error: 'Missing required fields: imageUrl and prompt' },
        { status: 400 }
      );
    }

    if (prompt.length < 10) {
      return NextResponse.json(
        { error: 'Prompt must be at least 10 characters long' },
        { status: 400 }
      );
    }

    // Validate duration if provided
    if (duration && ![5, 10].includes(duration)) {
      return NextResponse.json(
        { error: 'Duration must be 5 or 10 seconds' },
        { status: 400 }
      );
    }

    // Generate video using RunwayML
    const result = await generateVideoWithRunwayML(imageUrl, prompt, {
      duration,
      ratio,
      seed,
    });

    if (result.status === 'failed') {
      return NextResponse.json(
        { error: result.error || 'Video generation failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      generationId: result.id,
      status: result.status,
      videoUrl: result.videoUrl,
      progress: result.progress,
      createdAt: result.createdAt,
      completedAt: result.completedAt,
    });

  } catch (error) {
    console.error('Video generation error:', error);

    // Return more specific error messages
    let errorMessage = 'Internal server error';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Handle specific RunwayML API errors
      if (error.message.includes('400')) {
        statusCode = 400;
        // Extract the actual error message from RunwayML
        const match = error.message.match(/"error":"([^"]+)"/);
        if (match) {
          errorMessage = match[1];
        }
      } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
        statusCode = 401;
        errorMessage = 'Invalid or missing RunwayML API key';
      } else if (error.message.includes('429')) {
        statusCode = 429;
        errorMessage = 'Rate limit exceeded. Please try again later.';
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// GET endpoint to check generation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const generationId = searchParams.get('id');

    if (!generationId) {
      return NextResponse.json(
        { error: 'Missing generation ID' },
        { status: 400 }
      );
    }

    // Create RunwayML service instance
    const runwayService = createRunwayMLService();

    // Get generation status
    const result = await runwayService.getGenerationStatus(generationId);

    return NextResponse.json({
      id: result.id,
      status: result.status,
      progress: result.progress,
      videoUrl: result.videoUrl,
      error: result.error,
      createdAt: result.createdAt,
      completedAt: result.completedAt,
    });

  } catch (error) {
    console.error('Status check error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to check status';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
