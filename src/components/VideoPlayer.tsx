'use client';

import { useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, RotateCcw, Volume2, Download } from 'lucide-react';
import { VideoPlayerProps } from '@/types';

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl, isVisible, onComplete }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (isVisible) {
      // Auto-complete after 5 seconds for demo purposes
      const timer = setTimeout(() => {
        handleVideoEnd();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [isVisible, handleVideoEnd]);

  const handleVideoEnd = useCallback(() => {
    onComplete();
  }, [onComplete]);

  const handleReplay = () => {
    const video = videoRef.current;
    if (video) {
      video.currentTime = 0;
      video.play();
    }
  };

  const handleDownload = () => {
    if (videoUrl) {
      const link = document.createElement('a');
      link.href = videoUrl;
      link.download = `generated-video-${Date.now()}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="relative w-full max-w-2xl mx-auto"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.5 }}
        >
          {/* Video container with vintage styling */}
          <div className="relative vintage-frame rounded-lg overflow-hidden bg-black">
            {/* Film grain overlay */}
            <div className="film-grain absolute inset-0 z-10 pointer-events-none" />
            
            {/* Generated video */}
            {videoUrl ? (
              <video
                ref={videoRef}
                className="w-full aspect-video object-cover"
                controls
                autoPlay
                muted
                onEnded={handleVideoEnd}
                onError={(e) => {
                  console.error('Video playback error:', e);
                }}
              >
                <source src={videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            ) : (
              <div className="w-full aspect-video bg-gradient-to-br from-vintage-sepia to-vintage-gold flex items-center justify-center">
                <div className="text-center space-y-4">
                  <motion.div
                    className="w-16 h-16 mx-auto bg-white/20 rounded-full flex items-center justify-center"
                    animate={{
                      scale: [1, 1.1, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <Play className="w-8 h-8 text-white" />
                  </motion.div>
                  <div className="text-white">
                    <p className="font-kalam text-lg">Generated Video</p>
                    <p className="font-inter text-sm opacity-80">
                      Your imagination brought to life
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Custom controls overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 z-20">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handleReplay}
                    className="flex items-center justify-center w-10 h-10 bg-vintage-gold/20 hover:bg-vintage-gold/30 rounded-full transition-colors duration-200"
                    title="Replay video"
                  >
                    <RotateCcw className="w-5 h-5 text-white" />
                  </button>

                  {videoUrl && (
                    <button
                      onClick={handleDownload}
                      className="flex items-center justify-center w-10 h-10 bg-vintage-gold/20 hover:bg-vintage-gold/30 rounded-full transition-colors duration-200"
                      title="Download video"
                    >
                      <Download className="w-5 h-5 text-white" />
                    </button>
                  )}

                  <Volume2 className="w-5 h-5 text-white/80" />
                </div>

                <div className="flex items-center space-x-2">
                  <div className="text-white/80 font-poppins text-sm">
                    AI Generated Memory
                  </div>
                  <div className="w-2 h-2 bg-vintage-gold rounded-full animate-pulse" />
                </div>
              </div>
            </div>
            
            {/* Film projector loading animation when video is loading */}
            <motion.div
              className="absolute inset-0 flex items-center justify-center bg-black/50 z-30"
              initial={{ opacity: 1 }}
              animate={{ opacity: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="flex flex-col items-center space-y-4">
                <motion.div
                  className="w-16 h-16 border-4 border-vintage-gold/30 border-t-vintage-gold rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <p className="text-white font-poppins text-sm">
                  Loading your memory...
                </p>
              </div>
            </motion.div>
          </div>
          
          {/* Video metadata */}
          <motion.div
            className="mt-4 text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <p className="font-kalam text-vintage-brown/80 text-lg">
              ✨ Your imagination brought to life ✨
            </p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default VideoPlayer;
